# Pastorial - AI-Powered Sermon Generator

A Next.js application that helps pastors and theologians generate, polish, and analyze sermons using local AI models through Ollama and LMStudio.

## Features

- **Sermon Generation**: Create structured sermons with introduction, main points, and conclusion
- **Sermon Polishing**: Improve clarity, grammar, and coherence of sermon drafts
- **Contextual Insights**: Get relevant Bible passages and commentary suggestions
- **Draft Analysis**: Analyze sermons for tone, clarity, and cultural relevance
- **Local AI Models**: Works with Ollama and LMStudio for privacy and offline usage
- **Model Selection**: Choose from available models in your local AI services

## Prerequisites

You need to have at least one of the following AI services running locally:

### Ollama (Recommended)
1. Install [Ollama](https://ollama.ai)
2. Pull a model: `ollama pull llama3.2` (or any other model you prefer)
3. Ollama runs on `http://localhost:11434` by default

### LMStudio
1. Install [LMStudio](https://lmstudio.ai)
2. Download and load a model in LMStudio
3. Start the local server (usually on `http://localhost:1234`)

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy the environment file and configure if needed:
   ```bash
   cp .env .env.local
   ```
4. Start the development server:
   ```bash
   npm run dev
   ```

## Configuration

The application will automatically detect available AI services. You can configure the following environment variables in `.env.local`:

```env
# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434

# LMStudio Configuration
LMSTUDIO_BASE_URL=http://localhost:1234/v1
LMSTUDIO_API_KEY=lm-studio

# Default AI Model (will be overridden by user selection)
DEFAULT_AI_MODEL=ollama:llama3.2
```

## Usage

1. Open the application in your browser (usually `http://localhost:9002`)
2. The app will automatically detect available AI models from Ollama and LMStudio
3. Select your preferred model from the dropdown
4. Enter a topic or scripture passage
5. Configure the desired tone and target audience
6. Generate your sermon and use the additional tools to polish and analyze it

## Model Requirements

For best results, use models with good instruction-following capabilities:
- **Ollama**: llama3.2, llama3.1, mistral, codellama
- **LMStudio**: Any chat-optimized model (Llama, Mistral, etc.)

## Troubleshooting

- **No models available**: Ensure Ollama or LMStudio is running and has models loaded
- **Connection errors**: Check that the AI services are accessible on their default ports
- **Generation errors**: Try a different model or check the AI service logs
