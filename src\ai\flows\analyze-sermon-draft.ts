'use server';

/**
 * @fileOverview Analyzes sermon drafts for tone, clarity, and cultural relevance.
 *
 * - analyzeSermonDraft - A function that handles the sermon draft analysis process.
 * - AnalyzeSermonDraftInput - The input type for the analyzeSermonDraft function.
 * - AnalyzeSermonDraftOutput - The return type for the analyzeSermonDraft function.
 */

import {ai, DEFAULT_MODEL} from '@/ai/genkit';
import {z} from 'zod';

const AnalyzeSermonDraftInputSchema = z.object({
  draftText: z
    .string()
    .describe('The sermon draft text to be analyzed.'),
  systemPrompt: z
    .string()
    .optional()
    .describe('System prompt to guide the analysis. Overrides default system prompt if provided.'),
  modelId: z.string().optional().describe('The AI model to use for analysis. If not provided, uses the default model.'),
});
export type AnalyzeSermonDraftInput = z.infer<typeof AnalyzeSermonDraftInputSchema>;

const AnalyzeSermonDraftOutputSchema = z.object({
  toneAnalysis: z
    .string()
    .describe('Analysis of the sermon drafts tone.'),
  clarityAnalysis: z
    .string()
    .describe('Analysis of the sermon drafts clarity.'),
  culturalRelevanceAnalysis: z
    .string()
    .describe('Analysis of the sermon drafts cultural relevance, with a focus on a Nigerian, yet international, audience.'),
  overallFeedback: z
    .string()
    .describe('Overall feedback and suggestions for improvement.'),
});
export type AnalyzeSermonDraftOutput = z.infer<typeof AnalyzeSermonDraftOutputSchema>;

export async function analyzeSermonDraft(
  input: AnalyzeSermonDraftInput
): Promise<AnalyzeSermonDraftOutput> {
  const { draftText, systemPrompt, modelId } = input;
  const selectedModel = modelId || DEFAULT_MODEL;

  const defaultSystemPrompt = `You are an AI assistant designed to analyze sermon drafts for pastors.`;
  const finalSystemPrompt = systemPrompt || defaultSystemPrompt;

  const userPrompt = `Analyze the sermon draft provided, paying close attention to its tone, clarity, and cultural relevance for a Nigerian, yet international, audience. Provide specific feedback and suggestions for improvement in each of these areas, as well as overall feedback.

Draft Text: ${draftText}

Please provide your analysis in the following JSON format:
{
  "toneAnalysis": "Analysis of the sermon draft's tone",
  "clarityAnalysis": "Analysis of the sermon draft's clarity",
  "culturalRelevanceAnalysis": "Analysis of the sermon draft's cultural relevance, with a focus on a Nigerian, yet international, audience",
  "overallFeedback": "Overall feedback and suggestions for improvement"
}`;

  try {
    const result = await ai.generateStructured<AnalyzeSermonDraftOutput>(
      selectedModel,
      userPrompt,
      AnalyzeSermonDraftOutputSchema,
      {
        systemPrompt: finalSystemPrompt,
        temperature: 0.7,
        max_tokens: 2000,
      }
    );

    return result;
  } catch (error) {
    console.error('Error analyzing sermon draft:', error);
    throw new Error('Failed to analyze sermon draft. Please try again.');
  }
}
