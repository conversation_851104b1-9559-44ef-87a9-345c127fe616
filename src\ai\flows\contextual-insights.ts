'use server';

/**
 * @fileOverview A flow for suggesting relevant Bible passages and interpretations based on a sermon outline.
 *
 * - contextualInsights - A function that suggests Bible passages and interpretations.
 * - ContextualInsightsInput - The input type for the contextualInsights function.
 * - ContextualInsightsOutput - The return type for the contextualInsights function.
 */

import {ai, DEFAULT_MODEL} from '@/ai/genkit';
import {z} from 'zod';

const ContextualInsightsInputSchema = z.object({
  sermonOutline: z
    .string()
    .describe('The sermon outline to generate contextual insights for.'),
  modelId: z.string().optional().describe('The AI model to use for generating insights. If not provided, uses the default model.'),
});
export type ContextualInsightsInput = z.infer<typeof ContextualInsightsInputSchema>;

const ContextualInsightsOutputSchema = z.object({
  biblePassages: z
    .string()
    .describe('Relevant Bible passages based on the sermon outline.'),
  commentarySuggestions: z
    .string()
    .describe('Commentary suggestions for deeper understanding.'),
});
export type ContextualInsightsOutput = z.infer<typeof ContextualInsightsOutputSchema>;

export async function contextualInsights(input: ContextualInsightsInput): Promise<ContextualInsightsOutput> {
  const { sermonOutline, modelId } = input;
  const selectedModel = modelId || DEFAULT_MODEL;

  const systemPrompt = `You are an expert in biblical scripture and theology. Based on the provided sermon outline, suggest relevant Bible passages and commentary suggestions.`;

  const userPrompt = `Based on the provided sermon outline, suggest relevant Bible passages and commentary suggestions.

Sermon Outline: ${sermonOutline}

Provide the Bible passages and commentary suggestions in a clear and organized manner.

Please provide your response in the following JSON format:
{
  "biblePassages": "Relevant Bible passages based on the sermon outline",
  "commentarySuggestions": "Commentary suggestions for deeper understanding"
}`;

  try {
    const result = await ai.generateStructured<ContextualInsightsOutput>(
      selectedModel,
      userPrompt,
      ContextualInsightsOutputSchema,
      {
        systemPrompt,
        temperature: 0.7,
        max_tokens: 1500,
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating contextual insights:', error);
    throw new Error('Failed to generate contextual insights. Please try again.');
  }
}
