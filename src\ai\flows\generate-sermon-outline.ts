
'use server';

/**
 * @fileOverview AI-powered sermon outline generation flow.
 *
 * - generateSermonOutline - Generates a sermon outline based on a scripture or topic.
 * - GenerateSermonOutlineInput - The input type for the generateSermonOutline function.
 * - GenerateSermonOutlineOutput - The return type for the generateSermonOutline function.
 */

import {ai, DEFAULT_MODEL} from '@/ai/genkit';
import {z} from 'zod';

const GenerateSermonOutlineInputSchema = z.object({
  topicOrScripture: z
    .string()
    .describe('The scripture passage or topic for the sermon.'),
  desiredTone: z
    .string()
    .optional()
    .describe('The desired tone of the sermon (e.g., encouraging, challenging).'),
  targetAudience: z
    .string()
    .optional()
    .describe('Description of the target audience, e.g., "young adults in Nigeria".'),
  systemPrompt: z.string().optional().describe('An optional system prompt to guide the sermon generation.'),
  strictTopic: z.boolean().optional().describe('When true, the AI should adhere strictly to the provided topic and not deviate. Defaults to false.'),
  modelId: z.string().optional().describe('The AI model to use for generation. If not provided, uses the default model.'),
});
export type GenerateSermonOutlineInput = z.infer<typeof GenerateSermonOutlineInputSchema>;

const SermonPointSchema = z.object({
    point: z.string().describe('A main point or heading for a section of the sermon.'),
    explanation: z.string().describe('A detailed explanation of the point, formatted as an HTML string with paragraphs (<p>) and italics (<i>) for emphasis. This should weave together scriptural cross-references, key ideas, and practical applications. It should be written in a direct, narrative style.'),
    scripture: z.string().describe('The primary scripture reference for this point.')
});

const GenerateSermonOutlineOutputSchema = z.object({
  title: z.string().describe('The generated title for the sermon.'),
  mainText: z.string().describe('The primary Bible passage for the sermon.'),
  introduction: z.string().describe("The sermon's introduction, formatted as an HTML string with paragraphs (<p>) and italics (<i>)."),
  points: z.array(SermonPointSchema).describe('An array of the main points for the sermon, typically 3 or more.'),
  conclusion: z.string().describe("The sermon's conclusion, formatted as an HTML string with paragraphs (<p>) and italics (<i>), including a call to action or final thought."),
});
export type GenerateSermonOutlineOutput = z.infer<typeof GenerateSermonOutlineOutputSchema>;

export async function generateSermonOutline(
  input: GenerateSermonOutlineInput
): Promise<GenerateSermonOutlineOutput> {
  const modelId = input.modelId || DEFAULT_MODEL;

  // Build the system prompt
  let systemPrompt = `You are an experienced theologian and pastor, skilled in crafting engaging and relevant sermons. Your personality is sharp, insightful, and at times, unhinged and raw to get your point across.`;

  if (input.systemPrompt) {
    systemPrompt += `\n\n${input.systemPrompt}`;
  }

  // Build the user prompt
  const userPrompt = `Based on the given scripture or topic, generate a full, well-structured sermon. The sermon should be tailored to the specified target audience and tone. Consider the cultural context of a Nigerian, yet international, audience, ensuring the message is relatable and impactful.

The sermon must be structured with the following components:
1. **Title**: An engaging title for the sermon.
2. **Main Text**: The primary scripture reference for the entire sermon.
3. **Introduction**: An opening that grabs attention and introduces the topic.
4. **Points**: A list of 3 or more main points. Each point must have:
   - **Point**: The main heading for that section.
   - **Scripture**: A key scripture for that specific point.
   - **Explanation**: A detailed, direct, and extensive explanation of the point. This section should be substantial, at least 250 words long. Weave in multiple scriptural references for support. Do not be boring. Use real-life examples, including events from Nigerian news (cite the place and date), a relevant movie (cite the name and year), or a sharp "think of it this way" analogy. Avoid explicitly saying "Explanation:". Just deliver the message with depth and conviction.
5. **Conclusion**: A summary of the points and a powerful closing statement or call to action.

**Formatting Instructions**: For the 'introduction', 'explanation', and 'conclusion' fields, you MUST format the text as an HTML string. Use <p> tags for paragraphs and <i> tags for emphasis or italics. This will make the output more dynamic and readable.

Topic/Scripture: ${input.topicOrScripture}
Tone: ${input.desiredTone || 'encouraging'}
Target Audience: ${input.targetAudience || 'A diverse, international congregation with Nigerian roots.'}

${input.strictTopic ? 'You must adhere strictly to the provided topic and not introduce broader themes.' : ''}

The output must be a valid JSON object that follows this exact schema:
{
  "title": "string",
  "mainText": "string",
  "introduction": "string (HTML formatted)",
  "points": [
    {
      "point": "string",
      "scripture": "string",
      "explanation": "string (HTML formatted)"
    }
  ],
  "conclusion": "string (HTML formatted)"
}`;

  try {
    const result = await ai.generateStructured<GenerateSermonOutlineOutput>(
      modelId,
      userPrompt,
      GenerateSermonOutlineOutputSchema,
      {
        systemPrompt,
        temperature: 0.7,
        max_tokens: 4000,
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating sermon outline:', error);
    throw new Error('Failed to generate sermon outline. Please try again.');
  }
}

    