'use server';

/**
 * @fileOverview A flow to improve the system prompt using AI.
 *
 * - improveSystemPrompt - A function that takes a system prompt and returns an improved version.
 * - ImproveSystemPromptInput - The input type for the improveSystemPrompt function.
 * - ImproveSystemPromptOutput - The return type for the improveSystemPrompt function.
 */

import {ai, DEFAULT_MODEL} from '@/ai/genkit';
import {z} from 'zod';

const ImproveSystemPromptInputSchema = z.object({
  prompt: z.string().describe('The system prompt to improve.'),
  modelId: z.string().optional().describe('The AI model to use for improving the prompt. If not provided, uses the default model.'),
});
export type ImproveSystemPromptInput = z.infer<typeof ImproveSystemPromptInputSchema>;

const ImproveSystemPromptOutputSchema = z.object({
  improvedPrompt: z.string().describe('The improved system prompt.'),
});
export type ImproveSystemPromptOutput = z.infer<typeof ImproveSystemPromptOutputSchema>;

export async function improveSystemPrompt(input: ImproveSystemPromptInput): Promise<ImproveSystemPromptOutput> {
  const { prompt, modelId } = input;
  const selectedModel = modelId || DEFAULT_MODEL;

  const systemPrompt = `You are an AI prompt optimizer. Your task is to improve the given system prompt to be more clear, effective, and engaging.`;

  const userPrompt = `Please improve the following system prompt to be more clear, effective, and engaging:

Original Prompt: ${prompt}

Please provide your response in the following JSON format:
{
  "improvedPrompt": "The improved system prompt"
}`;

  try {
    const result = await ai.generateStructured<ImproveSystemPromptOutput>(
      selectedModel,
      userPrompt,
      ImproveSystemPromptOutputSchema,
      {
        systemPrompt,
        temperature: 0.7,
        max_tokens: 1000,
      }
    );

    return result;
  } catch (error) {
    console.error('Error improving system prompt:', error);
    throw new Error('Failed to improve system prompt. Please try again.');
  }
}
