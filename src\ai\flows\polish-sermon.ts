'use server';

/**
 * @fileOverview An AI agent that polishes sermon drafts for clarity, coherence, and impact.
 *
 * - polishSermon - A function that handles the sermon polishing process.
 * - PolishSermonInput - The input type for the polishSermon function.
 * - PolishSermonOutput - The return type for the polishSermon function.
 */

import {ai, DEFAULT_MODEL} from '@/ai/genkit';
import {z} from 'zod';
import { GenerateSermonOutlineOutput } from './generate-sermon-outline';

const PolishSermonInputSchema = z.object({
  sermon: z.custom<GenerateSermonOutlineOutput>(),
  systemPrompt: z.string().optional().describe('An optional system prompt to guide the polishing agent.'),
  modelId: z.string().optional().describe('The AI model to use for polishing. If not provided, uses the default model.'),
});
export type PolishSermonInput = z.infer<typeof PolishSermonInputSchema>;
export type PolishSermonOutput = GenerateSermonOutlineOutput;


export async function polishSermon(
  input: PolishSermonInput
): Promise<PolishSermonOutput> {
  const { sermon, systemPrompt, modelId } = input;
  const selectedModel = modelId || DEFAULT_MODEL;

  const polishText = async (text: string): Promise<string> => {
    try {
      const result = await ai.generateStructured<{ polishedText: string }>(
        selectedModel,
        `Review the text provided below. It is an HTML string. Your goal is to enhance its quality without losing the original author's voice.
        - Correct any grammatical errors, spelling mistakes, and typos within the HTML content.
        - Improve sentence structure for better flow and clarity.
        - Ensure that all statements are coherent and logically sound.
        - Fix any unfinished or awkward-sounding sentences.
        - Do NOT change the core message or theological points.
        - Do NOT alter the HTML structure (e.g., p, i tags). Your edits should only modify the text content within these tags.
        - The output must be a single, clean HTML string, preserving the original's tags.

        Text to Polish:
        ${text}

        Return a JSON object with the polished text in the 'polishedText' field.`,
        undefined,
        {
          systemPrompt: systemPrompt || `You are a meticulous editor AI with a deep understanding of theology and rhetoric. Your task is to polish a given piece of sermon text.`,
          temperature: 0.3,
          max_tokens: 2000,
        }
      );

      return result.polishedText || text;
    } catch (error) {
      console.error('Error polishing text:', error);
      return text; // Return original text if polishing fails
    }
  };

  const polishedIntroduction = await polishText(sermon.introduction);
  const polishedConclusion = await polishText(sermon.conclusion);
  const polishedPoints = await Promise.all(
    sermon.points.map(async (point) => {
      const polishedExplanation = await polishText(point.explanation);
      return { ...point, explanation: polishedExplanation };
    })
  );

  return {
    ...sermon,
    introduction: polishedIntroduction,
    points: polishedPoints,
    conclusion: polishedConclusion,
  };
}


