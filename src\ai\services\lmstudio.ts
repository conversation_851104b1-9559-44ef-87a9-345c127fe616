/**
 * LMStudio AI Service
 * Provides integration with LMStudio local AI models via OpenAI-compatible API
 */

export interface LMStudioModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

export interface LMStudioMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LMStudioChatRequest {
  model: string;
  messages: LMStudioMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string[];
  stream?: boolean;
}

export interface LMStudioChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class LMStudioService {
  private baseUrl: string;
  private apiKey: string;

  constructor(baseUrl?: string, apiKey?: string) {
    this.baseUrl = baseUrl || process.env.LMSTUDIO_BASE_URL || 'http://localhost:1234/v1';
    this.apiKey = apiKey || process.env.LMSTUDIO_API_KEY || 'lm-studio';
  }

  /**
   * Check if LMStudio is running and accessible
   */
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.ok;
    } catch (error) {
      console.error('LMStudio not available:', error);
      return false;
    }
  }

  /**
   * Get list of available models from LMStudio
   */
  async getModels(): Promise<LMStudioModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching LMStudio models:', error);
      return [];
    }
  }

  /**
   * Generate text using LMStudio chat completion
   */
  async generate(
    model: string,
    prompt: string,
    systemPrompt?: string,
    options?: {
      temperature?: number;
      max_tokens?: number;
      top_p?: number;
    }
  ): Promise<string> {
    try {
      const messages: LMStudioMessage[] = [];
      
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }
      
      messages.push({ role: 'user', content: prompt });

      const request: LMStudioChatRequest = {
        model,
        messages,
        temperature: options?.temperature ?? 0.7,
        max_tokens: options?.max_tokens ?? 2048,
        top_p: options?.top_p ?? 0.9,
        stream: false,
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`LMStudio API error: ${response.statusText}`);
      }

      const data: LMStudioChatResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response from LMStudio');
      }

      return data.choices[0].message.content;
    } catch (error) {
      console.error('Error generating with LMStudio:', error);
      throw error;
    }
  }

  /**
   * Generate structured output (JSON) using LMStudio
   */
  async generateStructured<T>(
    model: string,
    prompt: string,
    systemPrompt?: string,
    schema?: any,
    options?: {
      temperature?: number;
      max_tokens?: number;
      top_p?: number;
    }
  ): Promise<T> {
    try {
      // Add JSON format instruction to the prompt
      const jsonPrompt = `${prompt}\n\nPlease respond with valid JSON only. Do not include any explanatory text outside the JSON.`;
      
      const response = await this.generate(model, jsonPrompt, systemPrompt, options);
      
      // Try to parse the JSON response
      try {
        // Clean up the response to extract JSON
        const cleanedResponse = response.trim();
        let jsonStr = cleanedResponse;
        
        // If response is wrapped in code blocks, extract the JSON
        if (cleanedResponse.startsWith('```json')) {
          const match = cleanedResponse.match(/```json\s*([\s\S]*?)\s*```/);
          if (match) {
            jsonStr = match[1];
          }
        } else if (cleanedResponse.startsWith('```')) {
          const match = cleanedResponse.match(/```\s*([\s\S]*?)\s*```/);
          if (match) {
            jsonStr = match[1];
          }
        }
        
        return JSON.parse(jsonStr) as T;
      } catch (parseError) {
        console.error('Failed to parse JSON response:', response);
        throw new Error('Invalid JSON response from LMStudio');
      }
    } catch (error) {
      console.error('Error generating structured output with LMStudio:', error);
      throw error;
    }
  }
}

// Default instance
export const lmstudio = new LMStudioService();
