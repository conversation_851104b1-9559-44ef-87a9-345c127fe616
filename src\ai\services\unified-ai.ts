/**
 * Unified AI Service
 * Provides a unified interface for both Ollama and LMStudio
 */

import { OllamaService, OllamaModel } from './ollama';
import { LMStudioService, LMStudioModel } from './lmstudio';

export type AIProvider = 'ollama' | 'lmstudio';

export interface UnifiedModel {
  id: string;
  name: string;
  provider: AIProvider;
  size?: number;
  description?: string;
}

export interface GenerateOptions {
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  systemPrompt?: string;
}

export class UnifiedAIService {
  private ollama: OllamaService;
  private lmstudio: LMStudioService;

  constructor() {
    this.ollama = new OllamaService();
    this.lmstudio = new LMStudioService();
  }

  /**
   * Check which AI services are available
   */
  async getAvailableProviders(): Promise<AIProvider[]> {
    const providers: AIProvider[] = [];
    
    if (await this.ollama.isAvailable()) {
      providers.push('ollama');
    }
    
    if (await this.lmstudio.isAvailable()) {
      providers.push('lmstudio');
    }
    
    return providers;
  }

  /**
   * Get all available models from all providers
   */
  async getAllModels(): Promise<UnifiedModel[]> {
    const models: UnifiedModel[] = [];
    
    try {
      // Get Ollama models
      if (await this.ollama.isAvailable()) {
        const ollamaModels = await this.ollama.getModels();
        models.push(...ollamaModels.map((model: OllamaModel) => ({
          id: `ollama:${model.name}`,
          name: model.name,
          provider: 'ollama' as AIProvider,
          size: model.size,
          description: `${model.details?.family || 'Unknown'} - ${model.details?.parameter_size || 'Unknown size'}`,
        })));
      }
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
    }

    try {
      // Get LMStudio models
      if (await this.lmstudio.isAvailable()) {
        const lmstudioModels = await this.lmstudio.getModels();
        models.push(...lmstudioModels.map((model: LMStudioModel) => ({
          id: `lmstudio:${model.id}`,
          name: model.id,
          provider: 'lmstudio' as AIProvider,
          description: `LMStudio model - ${model.owned_by}`,
        })));
      }
    } catch (error) {
      console.error('Error fetching LMStudio models:', error);
    }

    return models;
  }

  /**
   * Generate text using the specified model
   */
  async generate(
    modelId: string,
    prompt: string,
    options: GenerateOptions = {}
  ): Promise<string> {
    const [provider, modelName] = this.parseModelId(modelId);
    
    switch (provider) {
      case 'ollama':
        return await this.ollama.generate({
          model: modelName,
          prompt,
          system: options.systemPrompt,
          options: {
            temperature: options.temperature,
            top_p: options.top_p,
            num_predict: options.max_tokens,
          },
        });
        
      case 'lmstudio':
        return await this.lmstudio.generate(
          modelName,
          prompt,
          options.systemPrompt,
          {
            temperature: options.temperature,
            max_tokens: options.max_tokens,
            top_p: options.top_p,
          }
        );
        
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Generate structured output (JSON) using the specified model
   */
  async generateStructured<T>(
    modelId: string,
    prompt: string,
    schema?: any,
    options: GenerateOptions = {}
  ): Promise<T> {
    const [provider, modelName] = this.parseModelId(modelId);
    
    switch (provider) {
      case 'ollama':
        return await this.ollama.generateStructured<T>({
          model: modelName,
          prompt,
          system: options.systemPrompt,
          options: {
            temperature: options.temperature,
            top_p: options.top_p,
            num_predict: options.max_tokens,
          },
        }, schema);
        
      case 'lmstudio':
        return await this.lmstudio.generateStructured<T>(
          modelName,
          prompt,
          options.systemPrompt,
          schema,
          {
            temperature: options.temperature,
            max_tokens: options.max_tokens,
            top_p: options.top_p,
          }
        );
        
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Parse model ID to extract provider and model name
   */
  private parseModelId(modelId: string): [AIProvider, string] {
    const parts = modelId.split(':');
    if (parts.length !== 2) {
      throw new Error(`Invalid model ID format: ${modelId}. Expected format: provider:modelname`);
    }
    
    const [provider, modelName] = parts;
    if (provider !== 'ollama' && provider !== 'lmstudio') {
      throw new Error(`Unsupported provider: ${provider}`);
    }
    
    return [provider as AIProvider, modelName];
  }

  /**
   * Get models for a specific provider
   */
  async getModelsForProvider(provider: AIProvider): Promise<UnifiedModel[]> {
    const allModels = await this.getAllModels();
    return allModels.filter(model => model.provider === provider);
  }
}

// Default instance
export const unifiedAI = new UnifiedAIService();
