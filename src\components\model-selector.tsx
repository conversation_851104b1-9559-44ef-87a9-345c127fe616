"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, RefreshCw, Server, Cpu } from "lucide-react";
import { unifiedAI, UnifiedModel, AIProvider } from "@/ai/services/unified-ai";

interface ModelSelectorProps {
  selectedModel?: string;
  onModelSelect: (modelId: string) => void;
  className?: string;
}

export function ModelSelector({ selectedModel, onModelSelect, className }: ModelSelectorProps) {
  const [models, setModels] = useState<UnifiedModel[]>([]);
  const [availableProviders, setAvailableProviders] = useState<AIProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadModels = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Check available providers
      const providers = await unifiedAI.getAvailableProviders();
      setAvailableProviders(providers);
      
      if (providers.length === 0) {
        setError("No AI providers are available. Please ensure Ollama or LMStudio is running.");
        setModels([]);
        return;
      }
      
      // Get all models
      const allModels = await unifiedAI.getAllModels();
      setModels(allModels);
      
      // Auto-select first model if none selected
      if (!selectedModel && allModels.length > 0) {
        onModelSelect(allModels[0].id);
      }
    } catch (err) {
      console.error('Error loading models:', err);
      setError('Failed to load models. Please check your AI services.');
      setModels([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadModels();
  }, []);

  const getProviderIcon = (provider: AIProvider) => {
    switch (provider) {
      case 'ollama':
        return <Server className="h-4 w-4" />;
      case 'lmstudio':
        return <Cpu className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getProviderColor = (provider: AIProvider) => {
    switch (provider) {
      case 'ollama':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'lmstudio':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatModelSize = (size?: number) => {
    if (!size) return '';
    const gb = size / (1024 * 1024 * 1024);
    return gb > 1 ? `${gb.toFixed(1)}GB` : `${(size / (1024 * 1024)).toFixed(0)}MB`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            Loading AI Models...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-red-600">AI Models Unavailable</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">{error}</p>
          <div className="space-y-2">
            <p className="text-sm font-medium">To use this application, please:</p>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Install and run <a href="https://ollama.ai" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Ollama</a> (default port 11434)</li>
              <li>• Or install and run <a href="https://lmstudio.ai" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">LMStudio</a> (default port 1234)</li>
            </ul>
          </div>
          <Button onClick={loadModels} className="mt-4" variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>AI Model Selection</span>
          <Button onClick={loadModels} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Provider Status */}
          <div className="flex flex-wrap gap-2">
            {availableProviders.map((provider) => (
              <Badge key={provider} className={getProviderColor(provider)}>
                {getProviderIcon(provider)}
                <span className="ml-1 capitalize">{provider}</span>
              </Badge>
            ))}
          </div>

          {/* Model Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Model:</label>
            <Select value={selectedModel} onValueChange={onModelSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Choose an AI model..." />
              </SelectTrigger>
              <SelectContent>
                {models.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        {getProviderIcon(model.provider)}
                        <span>{model.name}</span>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        {model.size && (
                          <span className="text-xs text-muted-foreground">
                            {formatModelSize(model.size)}
                          </span>
                        )}
                        <Badge variant="outline" className="text-xs">
                          {model.provider}
                        </Badge>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Selected Model Info */}
          {selectedModel && (
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-sm">
                <div className="font-medium">Selected Model:</div>
                {(() => {
                  const model = models.find(m => m.id === selectedModel);
                  if (!model) return <span className="text-muted-foreground">Unknown model</span>;
                  
                  return (
                    <div className="mt-1 space-y-1">
                      <div className="flex items-center gap-2">
                        {getProviderIcon(model.provider)}
                        <span>{model.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {model.provider}
                        </Badge>
                      </div>
                      {model.description && (
                        <div className="text-xs text-muted-foreground">{model.description}</div>
                      )}
                      {model.size && (
                        <div className="text-xs text-muted-foreground">
                          Size: {formatModelSize(model.size)}
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
